"""
Kaipoke Form Download Workflow (Final Corrected Version)
意图：根据高度可配置的任务列表，从Kaipoke系统下载各种指定样式的月度报告。

核心流程 (最终修正版):
根据用户最终反馈，实现按据点分组，在同一页面连续执行子任务的最高效、最稳定流程。

1. 按账号登录。
2. 在每个账号内，按据点对任务进行分组。
3. 对每个据点，执行一次性导航至“様式出力”页面。
4. 在“様式出力”页面上，循环执行该据点的所有子任务。
5. 每个子任务下载完成后，通过刷新页面来重置状态，为下一个子任务做准备。
"""

import asyncio
import os
from datetime import datetime
from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
from core.rpa_tools.kaipoke_common import get_previous_month_wareki
from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups
from core.gsuite.drive_client import DriveClient

async def group_tasks_by_account(tasks: list, common_config: dict):
    """按账号分组任务"""
    tasks_by_account = {}
    default_corp_id_env = common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID')
    default_login_id_env = common_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID')
    default_password_env = common_config.get('password_env', 'KAIPOKE_PASSWORD')
    default_account_key = f"{os.getenv(default_corp_id_env)}_{os.getenv(default_login_id_env)}"

    for task_config in tasks:
        task_corporation_id_env = task_config.get('corporation_id_env')
        task_member_login_id_env = task_config.get('member_login_id_env')
        task_password_env = task_config.get('password_env')
        account_key = default_account_key
        if task_corporation_id_env and task_member_login_id_env and task_password_env:
            account_key = f"{os.getenv(task_corporation_id_env)}_{os.getenv(task_member_login_id_env)}"

        if account_key not in tasks_by_account:
            tasks_by_account[account_key] = {
                'corporation_id': os.getenv(task_corporation_id_env or default_corp_id_env),
                'member_login_id': os.getenv(task_member_login_id_env or default_login_id_env),
                'password': os.getenv(task_password_env or default_password_env),
                'tasks': []
            }
        tasks_by_account[account_key]['tasks'].append(task_config)
    logger.info(f"📊 任务已按 {len(tasks_by_account)} 个账号分组。")
    return tasks_by_account

def group_tasks_by_facility(tasks: list):
    """在账号分组的基础上，按据点进一步分组任务"""
    tasks_by_facility = {}
    for task in tasks:
        element_text = task.get('element_text')
        if element_text not in tasks_by_facility:
            tasks_by_facility[element_text] = {
                'facility_name': task.get('facility_name'),
                'tasks': []
            }
        tasks_by_facility[element_text]['tasks'].append(task)
    logger.info(f"🏢 同一账号下的任务已按 {len(tasks_by_facility)} 个据点进一步分组。")
    return tasks_by_facility

async def execute_task_on_page(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """在“様式出力”页面上，执行单个下载任务"""
    page = selector_executor.page
    flow_type = task_config.get('flow_type', 'general')
    task_id = task_config.get('task_id', 'N/A')
    logger.info(f"🚀 Task {task_id}: Starting flow '{flow_type}'")

    wareki_month = get_previous_month_wareki()
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")

    # 1. 月份选择
    await selector_executor.smart_select("kaipoke_form_download", "selection", "month_selector", wareki_month)
    await page.wait_for_timeout(1000)

    # 2. 特殊流程2: 点击准备按钮
    if 'special_2' in flow_type:
        await selector_executor.smart_click("kaipoke_form_download", "navigation", "click_selector_pre")
        await page.wait_for_load_state('networkidle')

    # 3. 下载文件
    download_target_path = None
    async with page.expect_download(timeout=60000) as download_info:
        if 'special_1' in flow_type:
            await selector_executor.smart_click("kaipoke_form_download", "action", "click_selector_2_special_1", dynamic_params={'selector': task_config['click_selector_2']})
            await page.wait_for_load_state('networkidle')
            checkboxes = await page.query_selector_all("input[type='checkbox']")
            for checkbox in checkboxes:
                if await checkbox.is_visible(): await checkbox.check()
            await selector_executor.smart_click("kaipoke_form_download", "action", "click_selector_3_special_1")
        else: # general flow
            await selector_executor.smart_click("kaipoke_form_download", "action", "click_selector_2_general", dynamic_params={'selector': task_config['click_selector_2']})
        
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ Task {task_id}: File downloaded to: {download_target_path}")

    # 4. 确认下载
    if 'general' in flow_type:
        await selector_executor.smart_click("kaipoke_form_download", "action", "click_selector_3_general")
        await page.wait_for_timeout(3000)

    return download_target_path

async def process_facility_tasks(selector_executor: SelectorExecutor, facility_info: dict, common_config: dict, drive_client: DriveClient):
    """处理单个据点的所有连续子任务"""
    page = selector_executor.page
    facility_name = facility_info['facility_name']
    element_text = facility_info['element_text']
    tasks = facility_info['tasks']
    
    logger.info(f"🏢 Processing Facility: {facility_name} ({len(tasks)} tasks)")

    try:
        # 1. 一次性导航：进入据点 -> 进入“様式出力”页面
        facility_selection_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"
        await page.goto(facility_selection_url, wait_until='networkidle', timeout=30000)
        
        facility_xpath = f'//a[contains(text(), "{element_text}")]'
        await page.wait_for_selector(facility_xpath, timeout=20000)
        await page.click(facility_xpath, timeout=15000)
        await page.wait_for_load_state('networkidle', timeout=20000)
        
        await selector_executor.smart_hover("kaipoke_form_download", "navigation", "hover_selector")
        await page.wait_for_timeout(1500)
        
        async with page.expect_navigation(url="**/MEM095001.do**", timeout=20000):
            await selector_executor.smart_click("kaipoke_form_download", "navigation", "click_selector_1")
        
        await page.wait_for_load_state('networkidle', timeout=20000)
        logger.info(f"✅ Navigated to '様式出力' page for {facility_name}. Starting task loop.")

        # 2. 在同一页面上循环执行所有子任务
        for i, task_config in enumerate(tasks):
            try:
                downloaded_file_path = await execute_task_on_page(selector_executor, task_config, common_config['download_path'])
                if downloaded_file_path and common_config.get('gdrive_folder_id'):
                    drive_client.upload_file(downloaded_file_path, common_config['gdrive_folder_id'])
                    logger.info(f"✅ Task {task_config.get('task_id')}: File uploaded.")
                
                # 关键修复：如果不是最后一个任务，刷新页面以重置状态
                if i < len(tasks) - 1:
                    logger.info("🔄 Reloading page to reset state for the next task...")
                    await page.reload(wait_until='networkidle', timeout=30000)
                    logger.info("✅ Page reloaded.")

            except Exception as task_error:
                logger.error(f"❌ Task {task_config.get('task_id')} failed: {task_error}", exc_info=True)
                logger.info("Attempting to recover by reloading the page...")
                await page.reload(wait_until='networkidle', timeout=30000)

    except Exception as facility_error:
        logger.error(f"❌ Failed to process facility {facility_name}: {facility_error}", exc_info=True)

async def async_run(config: dict):
    """工作流异步主函数"""
    logger.info("🚀 Starting Kaipoke Form Download Workflow (Final Corrected Version)")
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    if not tasks:
        logger.error("❌ No tasks defined in configuration.")
        return

    tasks_by_account = await group_tasks_by_account(tasks, common_config)
    browser_manager = BrowserManager()
    drive_client = DriveClient()
    
    try:
        await browser_manager.start_browser(headless=common_config.get("headless", False))
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)

        for account_key, account_info in tasks_by_account.items():
            logger.info(f"🔑 Processing account: {account_key}")
            login_success = await kaipoke_login_direct(
                page, account_info['corporation_id'], account_info['member_login_id'],
                account_info['password'], common_config.get('login_url')
            )
            if not login_success:
                logger.error(f"❌ Login failed for account {account_key}, skipping.")
                continue
            
            await handle_kaipoke_login_popups(page, f"form_download_{account_key}")

            tasks_by_facility = group_tasks_by_facility(account_info['tasks'])
            for element_text, facility_info in tasks_by_facility.items():
                facility_info['element_text'] = element_text
                await process_facility_tasks(selector_executor, facility_info, common_config, drive_client)
                logger.info(f"--- Finished all tasks for facility: {facility_info['facility_name']} ---")

        logger.info("✅ All tasks for all accounts completed.")
    except Exception as e:
        logger.error(f"❌ A critical error occurred in the workflow: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()

def run(config: dict):
    """工作流同步入口函数"""
    asyncio.run(async_run(config))