"""
Kaipoke Form Download Workflow (Template-Based Version)
意图：一次性登录カイポケ系统，基于三种标准操作模板（A、B、C）完成37个账单预览文件的下载。

核心流程 (模板化版本):
1. 登录カイポケ系统
2. 进入服务总览页（レセプト菜单）
3. 主循环：遍历37个任务配置
   - 导航到目标链接识别码对应的页面
   - 根据流程类型调用对应的操作模板
   - 下载并重命名文件
   - 返回服务总览页

三种标准操作模板:
- 模板A: 国保連請求管理 -> 様式出力 -> 选择月份 -> 打印预览 -> 确认下载
- 模板B: 医療請求 -> 様式出力 -> 选择月份 -> 出力設定 -> 勾选选项 -> 最终下载
- 模板C: 介護請求 -> 様式出力 -> 准备按钮 -> 选择月份 -> 打印预览 -> 确认下载
"""

import asyncio
import os
from datetime import datetime
from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
from core.rpa_tools.kaipoke_common import get_previous_month_wareki
from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups
from core.gsuite.drive_client import DriveClient

async def template_a_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """
    模板A: 国保連請求管理流程
    操作序列: 悬停导航菜单 -> 点击"様式出力" -> 选择月份 -> 点击"打印预览" -> 确认下载
    """
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    
    logger.info(f"🅰️ Task {task_id}: Executing Template A flow")
    
    # 1. 导航: 悬停于"国保連請求管理"，点击"様式出力"
    await selector_executor.smart_hover("kaipoke_form_download", "template_a", "hover_selector")
    await page.wait_for_timeout(1500)
    
    async with page.expect_navigation(timeout=20000):
        await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_1")
    await page.wait_for_load_state('networkidle', timeout=20000)
    
    # 2. 选择月份
    wareki_month = get_previous_month_wareki()
    await selector_executor.smart_select("kaipoke_form_download", "template_a", "month_selector", wareki_month)
    await page.wait_for_timeout(1000)
    
    # 3. 下载文件
    download_target_path = None
    async with page.expect_download(timeout=60000) as download_info:
        # 点击打印预览（使用模板参数）
        await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_2", 
                                          dynamic_params={'selector': template_param})
        
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ Task {task_id}: File downloaded to: {download_target_path}")
    
    # 4. 确认下载
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_3")
        await page.wait_for_timeout(2000)
        logger.info(f"✅ Task {task_id}: Template A completed")
    except Exception as e:
        logger.warning(f"⚠️ Task {task_id}: Download confirmation failed (may be normal): {e}")
    
    return download_target_path

async def template_b_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """
    模板B: 医療請求流程
    操作序列: 悬停导航菜单 -> 点击"様式出力" -> 选择月份 -> 点击"出力設定" -> 勾选选项 -> 最终下载
    """
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    
    logger.info(f"🅱️ Task {task_id}: Executing Template B flow")
    
    # 1. 导航: 悬停于"医療請求"，点击"様式出力"
    await selector_executor.smart_hover("kaipoke_form_download", "template_b", "hover_selector")
    await page.wait_for_timeout(1500)
    
    async with page.expect_navigation(timeout=20000):
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_1")
    await page.wait_for_load_state('networkidle', timeout=20000)
    
    # 2. 选择月份
    wareki_month = get_previous_month_wareki()
    await selector_executor.smart_select("kaipoke_form_download", "template_b", "month_selector", wareki_month)
    await page.wait_for_timeout(1000)
    
    # 3. 点击出力設定（使用模板参数）
    await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_2", 
                                      dynamic_params={'selector': template_param})
    await page.wait_for_load_state('networkidle')
    await page.wait_for_timeout(1000)
    
    # 4. 勾选选项
    # 勾选-包括已输出
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_already_output")
    except Exception as e:
        logger.warning(f"⚠️ Task {task_id}: Already output checkbox not found: {e}")
    
    # 勾选-全选
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_select_all")
    except Exception as e:
        logger.warning(f"⚠️ Task {task_id}: Select all checkbox not found: {e}")
    
    await page.wait_for_timeout(1000)
    
    # 5. 最终下载
    download_target_path = None
    async with page.expect_download(timeout=60000) as download_info:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "final_download")
        
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ Task {task_id}: Template B completed - File downloaded to: {download_target_path}")
    
    return download_target_path

async def template_c_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """
    模板C: 介護請求流程
    操作序列: 悬停导航菜单 -> 点击"様式出力" -> 点击准备按钮 -> 选择月份 -> 点击"打印预览" -> 确认下载
    """
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '.box-refine :nth-child(1)')  # 默认参数
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")

    logger.info(f"🅲 Task {task_id}: Executing Template C flow")

    # 1. 导航: 悬停于"介護請求"，点击"様式出力"
    await selector_executor.smart_hover("kaipoke_form_download", "template_c", "hover_selector")
    await page.wait_for_timeout(1500)

    async with page.expect_navigation(timeout=20000):
        await selector_executor.smart_click("kaipoke_form_download", "template_c", "click_selector_1")
    await page.wait_for_load_state('networkidle', timeout=20000)

    # 2. 点击准备按钮
    await selector_executor.smart_click("kaipoke_form_download", "template_c", "preparation_button")
    await page.wait_for_load_state('networkidle')
    await page.wait_for_timeout(1000)

    # 3. 选择月份
    wareki_month = get_previous_month_wareki()
    await selector_executor.smart_select("kaipoke_form_download", "template_c", "month_selector", wareki_month)
    await page.wait_for_timeout(1000)

    # 4. 下载文件
    download_target_path = None
    async with page.expect_download(timeout=60000) as download_info:
        # 点击打印预览（使用模板参数）
        await selector_executor.smart_click("kaipoke_form_download", "template_c", "click_selector_2",
                                          dynamic_params={'selector': template_param})

        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ Task {task_id}: File downloaded to: {download_target_path}")

    # 5. 确认下载
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_c", "click_selector_3")
        await page.wait_for_timeout(2000)
        logger.info(f"✅ Task {task_id}: Template C completed")
    except Exception as e:
        logger.warning(f"⚠️ Task {task_id}: Download confirmation failed (may be normal): {e}")

    return download_target_path

async def template_bc_combined_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """
    组合流程B+C: 医療請求导航 + 介護請求准备按钮 + 医療請求下载流程
    """
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    
    logger.info(f"🅱️🅲 Task {task_id}: Executing Template B+C combined flow")
    
    # 1. 执行模板B的导航步骤
    await selector_executor.smart_hover("kaipoke_form_download", "template_b", "hover_selector")
    await page.wait_for_timeout(1500)
    
    async with page.expect_navigation(timeout=20000):
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_1")
    await page.wait_for_load_state('networkidle', timeout=20000)
    
    # 2. 执行模板C的准备按钮步骤
    await selector_executor.smart_click("kaipoke_form_download", "template_c", "preparation_button")
    await page.wait_for_load_state('networkidle')
    await page.wait_for_timeout(1000)
    
    # 3. 执行模板B的其余步骤（选择月份到最终下载）
    wareki_month = get_previous_month_wareki()
    await selector_executor.smart_select("kaipoke_form_download", "template_b", "month_selector", wareki_month)
    await page.wait_for_timeout(1000)
    
    # 点击出力設定
    await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_2", 
                                      dynamic_params={'selector': template_param})
    await page.wait_for_load_state('networkidle')
    await page.wait_for_timeout(1000)
    
    # 勾选选项
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_already_output")
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_select_all")
    except Exception as e:
        logger.warning(f"⚠️ Task {task_id}: Checkbox operations failed: {e}")
    
    await page.wait_for_timeout(1000)
    
    # 最终下载
    download_target_path = None
    async with page.expect_download(timeout=60000) as download_info:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "final_download")
        
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ Task {task_id}: Template B+C completed - File downloaded to: {download_target_path}")
    
    return download_target_path

async def execute_task_with_template(selector_executor: SelectorExecutor, task_config: dict, download_path: str):
    """根据流程类型调用对应的操作模板"""
    flow_type = task_config.get('flow_type', 'A')
    task_id = task_config.get('task_id', 'N/A')

    logger.info(f"🎯 Task {task_id}: Flow type '{flow_type}' - Selecting template")

    try:
        if flow_type == 'A':
            return await template_a_flow(selector_executor, task_config, download_path)
        elif flow_type == 'B':
            return await template_b_flow(selector_executor, task_config, download_path)
        elif flow_type == 'C':
            return await template_c_flow(selector_executor, task_config, download_path)
        elif flow_type == 'B+C':
            return await template_bc_combined_flow(selector_executor, task_config, download_path)
        else:
            logger.error(f"❌ Task {task_id}: Unknown flow type '{flow_type}'")
            return None
    except Exception as e:
        logger.error(f"❌ Task {task_id}: Template execution failed: {e}", exc_info=True)
        return None

async def navigate_to_service_overview(page):
    """导航到服务总览页"""
    logger.info("🏠 Navigating to service overview page (レセプト)")

    # 点击主菜单"レセプト"
    receipt_menu_selector = ".mainCtg li:nth-of-type(1) a"
    await page.wait_for_selector(receipt_menu_selector, timeout=20000)
    await page.click(receipt_menu_selector)
    await page.wait_for_load_state('networkidle', timeout=20000)

    logger.info("✅ Successfully navigated to service overview page")

async def navigate_to_facility(page, element_text: str):
    """导航到指定的据点页面"""
    logger.info(f"🏢 Navigating to facility: {element_text}")

    # 使用XPath查找包含目标文本的链接
    facility_xpath = f'//a[contains(text(), "{element_text}")]'

    try:
        await page.wait_for_selector(facility_xpath, timeout=20000)
        await page.click(facility_xpath, timeout=15000)
        await page.wait_for_load_state('networkidle', timeout=20000)
        logger.info(f"✅ Successfully navigated to facility: {element_text}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to navigate to facility {element_text}: {e}")
        return False

async def return_to_service_overview(page):
    """返回到服务总览页"""
    logger.info("🔙 Returning to service overview page")

    try:
        # 尝试多种返回方式
        overview_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"
        await page.goto(overview_url, wait_until='networkidle', timeout=30000)
        logger.info("✅ Successfully returned to service overview page")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to return to service overview: {e}")
        return False

def group_tasks_by_facility(tasks: list):
    """按据点分组任务，优化执行效率"""
    facility_groups = {}

    for task in tasks:
        element_text = task.get('element_text', '')
        if element_text not in facility_groups:
            facility_groups[element_text] = []
        facility_groups[element_text].append(task)

    logger.info(f"📊 任务已按 {len(facility_groups)} 个据点分组")
    for element_text, group_tasks in facility_groups.items():
        logger.info(f"   🏢 {element_text}: {len(group_tasks)}个任务")

    return facility_groups

async def process_facility_group(selector_executor: SelectorExecutor, element_text: str,
                               facility_tasks: list, common_config: dict, drive_client: DriveClient):
    """处理单个据点的所有任务"""
    page = selector_executor.page
    logger.info(f"🏢 开始处理据点: {element_text} ({len(facility_tasks)}个任务)")

    try:
        # 1. 导航到据点
        if not await navigate_to_facility(page, element_text):
            logger.error(f"❌ 无法导航到据点: {element_text}")
            return 0

        successful_tasks = 0

        # 2. 在同一据点页面连续执行所有任务
        for i, task_config in enumerate(facility_tasks):
            task_id = task_config.get('task_id', 'N/A')
            logger.info(f"📋 执行任务 {i+1}/{len(facility_tasks)}: {task_id}")

            try:
                # 执行任务模板
                downloaded_file_path = await execute_task_with_template(
                    selector_executor, task_config, common_config['download_path']
                )

                if downloaded_file_path:
                    # 上传到Google Drive
                    if common_config.get('gdrive_folder_id'):
                        try:
                            drive_client.upload_file(downloaded_file_path, common_config['gdrive_folder_id'])
                            logger.info(f"✅ Task {task_id}: 文件已上传到Google Drive")
                        except Exception as upload_error:
                            logger.error(f"❌ Task {task_id}: 上传失败: {upload_error}")

                    successful_tasks += 1
                    logger.info(f"✅ Task {task_id}: 完成")
                else:
                    logger.error(f"❌ Task {task_id}: 下载失败")

                # 如果不是最后一个任务，返回到据点主页面准备下一个任务
                if i < len(facility_tasks) - 1:
                    logger.info("🔄 返回据点主页面准备下一个任务...")
                    await page.go_back()
                    await page.wait_for_load_state('networkidle', timeout=20000)
                    await page.wait_for_timeout(1000)

            except Exception as task_error:
                logger.error(f"❌ Task {task_id}: 执行失败: {task_error}", exc_info=True)
                # 尝试返回据点主页面
                try:
                    await page.go_back()
                    await page.wait_for_load_state('networkidle', timeout=10000)
                except:
                    # 如果返回失败，重新导航到据点
                    await navigate_to_facility(page, element_text)

        logger.info(f"✅ 据点 {element_text} 处理完成: {successful_tasks}/{len(facility_tasks)} 任务成功")
        return successful_tasks

    except Exception as facility_error:
        logger.error(f"❌ 据点 {element_text} 处理失败: {facility_error}", exc_info=True)
        return 0

async def async_run(config: dict):
    """工作流异步主函数"""
    logger.info("🚀 Starting Kaipoke Form Download Workflow (Template-Based Version)")

    common_config = config.get('config', {})
    tasks = config.get('tasks', [])

    if not tasks:
        logger.error("❌ No tasks defined in configuration.")
        return

    browser_manager = BrowserManager()
    drive_client = DriveClient()

    try:
        await browser_manager.start_browser(headless=common_config.get("headless", False))
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)

        # 1. 登录カイポケ系统
        logger.info("🔑 Logging into Kaipoke system")
        login_success = await kaipoke_login_direct(
            page,
            os.getenv(common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID')),
            os.getenv(common_config.get('member_login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID')),
            os.getenv(common_config.get('password_env', 'KAIPOKE_PASSWORD')),
            common_config.get('login_url')
        )

        if not login_success:
            logger.error("❌ Login failed, aborting workflow.")
            return

        await handle_kaipoke_login_popups(page, "form_download_template_based")

        # 2. 进入服务总览页
        await navigate_to_service_overview(page)

        # 3. 按据点分组任务
        facility_groups = group_tasks_by_facility(tasks)

        # 4. 按据点处理任务
        total_successful = 0
        total_tasks = len(tasks)

        for element_text, facility_tasks in facility_groups.items():
            try:
                successful_count = await process_facility_group(
                    selector_executor, element_text, facility_tasks, common_config, drive_client
                )
                total_successful += successful_count

                # 返回服务总览页准备下一个据点
                await return_to_service_overview(page)
                await page.wait_for_timeout(2000)

            except Exception as group_error:
                logger.error(f"❌ 据点组处理失败 {element_text}: {group_error}", exc_info=True)
                # 尝试恢复到服务总览页
                await return_to_service_overview(page)

        logger.info(f"🎉 工作流完成: {total_successful}/{total_tasks} 任务成功")

    except Exception as e:
        logger.error(f"❌ 工作流执行过程中发生严重错误: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()

def run(config: dict):
    """工作流同步入口函数"""
    asyncio.run(async_run(config))
