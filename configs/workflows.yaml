# 1. Kanamic CSV Download Workflow (ACTIVE - 重構・テスト済み)
# 意図：カナミックから地域密着型介護福祉施設入所者生活介護のCSVデータを
#       ダウンロードしてGoogle Driveにアップロードする
# 特徴：選択器優先、Agent備用、完全自動化
# --------------------------------------------------------------------------
kanamic_csv_download:
  intent: "カナミックから地域密着型介護福祉施設入所者生活介護のCSVデータをダウンロードしてGoogle Driveにアップロードする（MCP強化版）"
  config:
    login_url: "https://bi.kanamic.net/josso/signon/login.do?josso_back_to=https://portal.kanamic.net/tritrus/josso_security_check"
    username_env: "KANAMIC_USERNAME"
    password_env: "KANAMIC_PASSWORD"
    download_path: "/tmp/kanamic_csv_downloads"
    gdrive_folder_id: "1tOnAiIDrNDafJmZrLSr2EL95Oq0v-CA0"
    
    # 🆕 MCP関連設定
    mcp_config:
      enabled: true
      timeout: 10000
      fallback_priority: 2  # 1=セレクタ, 2=MCP, 3=Agent
      smart_retry: true

# --------------------------------------------------------------------------
# 2. Kanamic Download Workflow (ACTIVE - 重構・テスト済み)
# 意図：カナミックシステムにログインし、指定された各事業所の月次データを
#       ダウンロードし、指定のGoogle DriveとSheetに保存する
# 特徴：選択器優先、Agent備用、複数事業所対応
# --------------------------------------------------------------------------
kanamic_download:
  intent: "カナミックシステムにログインし、指定された各事業所の月次データをダウンロードし、指定のGoogle DriveとSheetに保存する"
  config:
    login_url: "https://portal.kanamic.net/tritrus/index/"
    account: "<EMAIL>"
    password_env: KANAMIC_PASSWORD
    download_path: "/tmp/kanamic_downloads"
    gdrive_folder_id: "0ANZ6FwO1h9esUk9PVA"
    checkbox_targets:
      - "あおぞら介護ステーション福岡居宅介護支援事業所"
      - "あおぞら介護ステーション居宅介護支援事業所"
  tasks:
    # --- 合并任务：福岡居宅+居宅介護支援事業所 ---
    - task_id: "kanamic_fukuoka_kyotaku_kaigo"
      target: "「あおぞら介護ステーション福岡居宅介護支援事業所」と「居宅介護支援事業所」のサービス実績CSVを分別ダウンロードし、処理する"
      params:
        main_menu_target: "業務帳票"
        report_menu_target: "010 請求状況一覧"
        # report_type_target: "居宅介護支援"  # 不需要选择服务类型
        # 分步下载流程配置
        download_workflow: "SEQUENTIAL_CHECKBOX_DOWNLOAD"
        sequential_downloads:
          # 第一次下载：福岡居宅介護支援事業所
          - step: 1
            description: "あおぞら介護ステーション福岡居宅介護支援事業所データダウンロード"
            checkbox_selector: "#checkItem0"
            download_button_selector: "#doCsv-2"
            target_sheet_id: "1hfjiOMJoTGgAlrTyBxEVxP_qM4LaWor228awBOF7CzY"
            filter_by:
              column: "事業所名"
              value: "あおぞら介護ステーション福岡居宅介護支援事業所"
            header_row: 4  # 表头在第4行
            paste_start_row: 5  # 数据从第5行开始
            pre_paste_updates:
              - cell: "B1"
                value_source: "xlsx_cell"
                source_cell: "B2"  # 从源数据B2获取
                format: "date"  # 转换为2025/05/01格式
              - cell: "B2"
                value_source: "xlsx_cell"
                source_cell: "B4"  # 从源数据B4获取
          # 第二次下载：居宅介護支援事業所
          - step: 2
            description: "あおぞら介護ステーション居宅介護支援事業所データダウンロード"
            uncheck_previous: "#checkItem0"  # 先取消上一个复选框
            checkbox_selector: "#checkItem4"
            download_button_selector: "#doCsv-2"
            target_sheet_id: "1vyjCi29ijsrNMgcr33eKQ5cnkxscRfIu3XPHLdFsQBk"
            filter_by:
              column: "事業所名"
              value: "あおぞら介護ステーション居宅介護支援事業所"
            header_row: 3  # 表头在第3行
            paste_start_row: 4  # 数据从第4行开始
            pre_paste_updates:
              - cell: "B1"
                value_source: "xlsx_cell"
                source_cell: "B4"  # 从源数据B4获取
        date_handling_method: "SELECT_PREVIOUS_MONTH_JS"
        target_drive_folder_id: "0ANZ6FwO1h9esUk9PVA"
    # --- Task from CSV row 4: 1690_地域密着型介護老人福祉施設入居者生活介護 ---
    - task_id: "kanamic_1690_chiki_mitchaku"
      target: "「1690_地域密着型介護老人福祉施設入居者生活介護」の債権データをダウンロードし、処理する"
      params:
        main_menu_target: "債権・会計"
        report_menu_target: "債権管理"
        report_type_target: null
        checkbox_target: null
        download_button_target: "債権一覧をCSV出力"
        date_handling_method: "TYPE_PREVIOUS_MONTH"
        file_name_template: "梅ヶ丘債権データ特養・短期（{cell_C2}実績）{datetime}.xlsx"
        data_processing_rules:
          - target_sheet_id: "1q7Z-qQKwDr_nSG73M4NvPicne6sFsr1vAYRvAtJLEQk"
            header_row: 1
            paste_start_row: 4
        target_drive_folder_id: "0ANZ6FwO1h9esUk9PVA"

# --------------------------------------------------------------------------
# 3. Kaipoke Monthly Report Workflow (ACTIVE - 重構・テスト済み)
# 意図：カイポケにログインし、指定されたすべての通所介護拠点の月次実績データを
#       ダウンロードし、リネームしてGoogle Driveに保存する
# 特徴：選択器優先、Agent備用、多拠点対応、アカウント自動切換
# --------------------------------------------------------------------------
kaipoke_monthly_report:
  intent: "カイポケにログインし、指定されたすべての通所介護拠点の月次実績データをダウンロードし、リネームしてGoogle Driveに保存する"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    corporation_id_env: "KAIPOKE_CORPORATION_ID"
    member_login_id_env: "KAIPOKE_MEMBER_LOGIN_ID"
    password_env: "KAIPOKE_PASSWORD"
    download_path: "/tmp/kaipoke_downloads"
    gdrive_folder_id: "1tOnAiIDrNDafJmZrLSr2EL95Oq0v-CA0"
  tasks:
    - task_id: "get_aozora_day_service_report"
      target: "あおぞらデイサービスセンターの月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター"
        element_text: "通所介護/4670106956"
        output_filename_pattern: "{month}あおぞらデイサービスセンター.xlsx"

    - task_id: "get_tagami_day_service_report"
      target: "あおぞらデイサービス田上の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービス田上"
        element_text: "通所介護/4690102746"
        output_filename_pattern: "{month}あおぞらデイサービス田上.xlsx"

    - task_id: "get_umi_day_service_report"
      target: "あおぞらデイサービス宇美の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービス宇美"
        element_text: "通所介護/4073800759"
        output_filename_pattern: "{month}あおぞらデイサービス宇美.xlsx"

    - task_id: "get_shimoarata_day_service_report"
      target: "あおぞらデイサービスセンター下荒田の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター下荒田"
        element_text: "通所介護/4670112806"
        output_filename_pattern: "{month}あおぞらデイサービスセンター下荒田.xlsx"

    - task_id: "get_nanei_day_service_report"
      target: "あおぞらデイサービスセンター南栄の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター南栄"
        element_text: "通所介護/4670113358"
        output_filename_pattern: "{month}あおぞらデイサービスセンター南栄.xlsx"

    - task_id: "get_nokae_day_service_report"
      target: "あおぞらデイサービス野芥の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービス野芥"
        element_text: "通所介護/4071405262"
        output_filename_pattern: "{month}あおぞらデイサービス野芥.xlsx"

    - task_id: "get_hakata_day_service_report"
      target: "あおぞらデイサービスセンター博多の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター博多"
        element_text: "通所介護/4070904703"
        output_filename_pattern: "{month}あおぞらデイサービスセンター博多.xlsx"

    - task_id: "get_shichifuku_day_service_report"
      target: "あおぞらデイサービス七福の里の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービス七福の里"
        element_text: "通所介護/4690103124"
        output_filename_pattern: "{month}あおぞらデイサービス七福の里.xlsx"

    - task_id: "get_urara_day_service_report"
      target: "あおぞらデイサービスうららの月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスうらら"
        element_text: "通所介護/4691200622"
        output_filename_pattern: "{month}あおぞらデイサービスうらら.xlsx"

    - task_id: "get_nagayoshi_day_service_report"
      target: "あおぞらデイサービスセンター永吉の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター永吉"
        element_text: "通所介護/4690103066"
        output_filename_pattern: "{month}あおぞらデイサービスセンター永吉.xlsx"
        # 永吉拠点使用不同的登录账号（从环境变量读取）
        corporation_id_env: "KAIPOKE_NAGAYOSHI_CORPORATION_ID"
        member_login_id_env: "KAIPOKE_NAGAYOSHI_MEMBER_LOGIN_ID"
        password_env: "KAIPOKE_NAGAYOSHI_PASSWORD"

    - task_id: "get_higashisengoku_day_service_report"
      target: "あおぞらデイサービスセンター東千石の月次実績レポートを取得"
      params:
        service_center_name: "あおぞらデイサービスセンター東千石"
        element_text: "通所介護/4670113754"
        output_filename_pattern: "{month}あおぞらデイサービスセンター東千石.xlsx"
        # 東千石拠点使用不同的登录账号（从环境变量读取）
        corporation_id_env: "KAIPOKE_HIGASHISENGOKU_CORPORATION_ID"
        member_login_id_env: "KAIPOKE_HIGASHISENGOKU_MEMBER_LOGIN_ID"
        password_env: "KAIPOKE_HIGASHISENGOKU_PASSWORD"

# --------------------------------------------------------------------------
# 4. Kaipoke Performance Report Workflow (NEW - REFACTORED)
# 意図：カイポケにログインし、指定された事業拠点（福岡・鹿児島）の実績を
#       抽出し、Google Sheetに転記する
# 特徴：設定ファイル駆動、単一コードベースで複数拠点を処理
# --------------------------------------------------------------------------
kaipoke_performance_report:
  intent: "カイポケにログインし、福岡と鹿児島の事業拠点の月次実績を抽出し、指定のGoogle Sheetに転記する"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    spreadsheet_id: "1duAFd9WNtlBs6bUwA1om6ePDJzIzYOTZMKXaXiJjQ2k"
    corporation_id_env: "KAIPOKE_CORPORATION_ID" # 修正：正确的环境变量名
    login_id_env: "KAIPOKE_MEMBER_LOGIN_ID"     # 修正：正确的环境变量名
    password_env: "KAIPOKE_PASSWORD"   # Default password from .env

  tasks:


    - task_id: "kaipoke_fukuoka_performance"
      facility_name: "福岡"
      service_center_name: "あおぞら介護ステーション福岡"
      element_text: "障害者総合支援(訪問系)/4011000983"
      target_sheet_name: "実績貼り付け_福岡"
      status_cell: "年月指定!B5"
      target_month_cell: "年月指定!B1"
      clear_range: "実績貼り付け_福岡!A:T"
      paste_range: "実績貼り付け_福岡!C1:R15000"
      year_paste_cell: "実績貼り付け_福岡!A1"
      month_paste_range: "実績貼り付け_福岡!B:B"
      # 新增：T1和T2位置配置
      total_count_cell: "実績貼り付け_福岡!T1"  # T1位置：页面显示的合计数（.pager-btm p的内容）
      user_count_cell: "実績貼り付け_福岡!T2"   # T2位置：网站实际数据总量（用于与T1对比验证）

    - task_id: "kaipoke_kagoshima_performance"
      facility_name: "鹿児島"
      service_center_name: "あおぞら介護ステーション"
      element_text: "障害者総合支援(訪問系)/4610101257"
      target_sheet_name: "実績貼り付け_鹿児島"
      status_cell: "年月指定!B4"
      target_month_cell: "年月指定!B1"
      clear_range: "実績貼り付け_鹿児島!A:T"
      paste_range: "実績貼り付け_鹿児島!C1:R15000"
      year_paste_cell: "実績貼り付け_鹿児島!A1"
      month_paste_range: "実績貼り付け_鹿児島!B:B"
      # 新增：T1和T2位置配置
      total_count_cell: "実績貼り付け_鹿児島!T1"  # T1位置：页面显示的合计数（.pager-btm p的内容）
      user_count_cell: "実績貼り付け_鹿児島!T2"   # T2位置：网站实际数据総量（用于与T1对比验证）

# --------------------------------------------------------------------------
# 5. Kaipoke Daily Performance Report Workflow (NEW - MCP強化版)
# 意図：カイポケにログインし、指定された事業拠点の提供日別実績登録データを
#       日付別に抽出し、Google Sheetに転記する
# 特徴：MCP機制、現有モジュール再利用、日付循環処理、分頁データ抽出
# --------------------------------------------------------------------------
kaipoke_daily_performance_report:
  intent: "カイポケにログインし、指定された事業拠点の提供日別実績登録データを日付別に抽出し、Google Sheetに転記する（MCP強化版）"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    spreadsheet_id: "17O-LFZ-RQTHa-ylnrwf40Q3HRcmH1rJPvV1YjdxR0tI"  # 保持原有ID，由用户修改GAS代码
    corporation_id_env: "KAIPOKE_CORPORATION_ID"
    login_id_env: "KAIPOKE_MEMBER_LOGIN_ID"
    password_env: "KAIPOKE_PASSWORD"

    # 🆕 MCP関連設定
    mcp_config:
      enabled: true
      timeout: 10000
      fallback_priority: 2  # 1=セレクタ, 2=MCP, 3=Agent
      smart_retry: true

  tasks:


    - task_id: "kaipoke_hukoka_daily_performance"
      facility_name: "福岡"
      service_center_name: "あおぞら介護ステーション福岡"
      element_text: "訪問介護/4071404182"  # RPA代码中的精确据点标识
      target_sheet_name: "福岡"  # RPA代码中的目标Sheet名
      target_range: "福岡!A1:J20000"  # RPA代码中的数据范围
      # 日期处理配置
      date_processing:
        target_month: "previous"  # 前月処理
        date_format: "japanese"   # 和暦格式

      # GAS触发配置（基于RPA代码）
      gas_trigger:
        url: "https://script.google.com/macros/s/AKfycbx_UNE9NNQ6SPTJIeoYC5YcHklcCaX_LvxjPZkldy6ekNiV3aLbZxAFaTwrpBmXhh_J/exec"
        password: "doFormat_Fukuoka"  # 福岡シートの処理


    - task_id: "kaipoke_kagoshima_daily_performance"
      facility_name: "鹿児島"
      service_center_name: "あおぞら介護ステーション"
      element_text: "訪問介護/4670105586"  # RPA代码中的精确据点标识
      target_sheet_name: "鹿児島"  # RPA代码中的目标Sheet名
      target_range: "鹿児島!A1:J20000"  # RPA代码中的数据范围

      # GAS触发配置（基于RPA代码）
      gas_trigger:
        url: "https://script.google.com/macros/s/AKfycbx_UNE9NNQ6SPTJIeoYC5YcHklcCaX_LvxjPZkldy6ekNiV3aLbZxAFaTwrpBmXhh_J/exec"
        password: "doFormat_Kagoshima"  # 鹿児島シートの処理

# --------------------------------------------------------------------------
# 6. Kaipoke Billing Workflow (NEW - MCP強化版)
# 意図：Kaipoke账单数据批量下载和处理工作流
#       登录Kaipoke系统，批量处理20个据点的账单数据下载
#       CSV编码转换、数据分类、Google Sheets多Sheet创建
# 特徴：MCP機制、批量写入风险控制、智能数据分类、据点导航管理
# --------------------------------------------------------------------------
kaipoke_billing_workflow:
  intent: "Kaipoke账单数据批量下载和处理工作流（MCP強化版）"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    corporation_id_env: "KAIPOKE_CORPORATION_ID"
    login_id_env: "KAIPOKE_MEMBER_LOGIN_ID"
    password_env: "KAIPOKE_PASSWORD"
    download_path: "/tmp/kaipoke_billing_downloads"

    # 🆕 批量处理风险控制
    batch_size: 5  # 每批处理5个据点

    # Google Drive和Sheets配置（用户自行设置）
    # spreadsheet_id: "YOUR_SPREADSHEET_ID"  # 主Spreadsheet（可选）
    # gdrive_folder_id: "YOUR_FOLDER_ID"     # 目标文件夹（可选）

    # 🆕 MCP関連設定
    mcp_config:
      enabled: true
      timeout: 10000
      fallback_priority: 2  # 1=セレクタ, 2=MCP, 3=Agent
      smart_retry: true

  tasks:
    # 20个据点配置（基于RPA代码提供的据点列表）
    - facility_name: "あおぞら介護ステーション"
      element_text: "障害者総合支援(訪問系)/4610101257"
      service_type: "障害者総合支援"

    - facility_name: "訪問看護ステーション"
      element_text: "訪問介護/4670105586"
      service_type: "訪問看護"

    - facility_name: "あおぞらデイサービスセンター"
      element_text: "通所介護/4670106956"
      service_type: "通所介護"

    - facility_name: "あおぞら介護ステーション　居宅介護支援事業所"
      element_text: "訪問看護/4660190861"
      service_type: "訪問看護"

    - facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問介護/4071404182"
      service_type: "訪問介護"

    - facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問看護/4060391200"
      service_type: "訪問看護"

    - facility_name: "あおぞら介護ステーション福岡"
      element_text: "障害者総合支援(訪問系)/4011000983"
      service_type: "障害者総合支援"

    - facility_name: "あおぞらケアテクノロジー"
      element_text: "福祉用具貸与/4670113242"
      service_type: "福祉用具貸与"

    - facility_name: "あおぞらデイサービス田上"
      element_text: "通所介護/4690102746"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター宇美"
      element_text: "通所介護/4073800759"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター下荒田"
      element_text: "通所介護/4670112806"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター南栄"
      element_text: "通所介護/4670113358"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター野芥"
      element_text: "通所介護/4071405262"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター博多"
      element_text: "通所介護/4070904703"
      service_type: "通所介護"

    - facility_name: "あおぞらデイサービスセンター東千石"
      element_text: "通所介護/4670112863"
      service_type: "通所介護"

    - facility_name: "デイサービス七福の里"
      element_text: "通所介護/4690103124"
      service_type: "通所介護"

    - facility_name: "デイサービスうらら"
      element_text: "通所介護/4691200622"
      service_type: "通所介護"

    - facility_name: "訪問看護ステーションあおぞら姶良"
      element_text: "訪問看護/4664590280"
      service_type: "訪問看護"

    - facility_name: "訪問看護ステーションあおぞら谷山"
      element_text: "訪問看護/4660191471"
      service_type: "訪問看護"

    - facility_name: "あおぞらデイサービスセンター永吉"
      element_text: "通所介護/4690103066"
      service_type: "通所介護"


kaipoke_tennki_refactored:
  # カイポケ実績・看護記録データの自動入力・検証・エラー収集（リファクタ・データ保護・多ブラウザ対応）
  # 本ワークフローは、各拠点のGoogle Sheets「看護記録」データを自動でカイポケに入力し、エラーを収集・検証します。
  # 特徴：
  #   - データ量・利用者数に応じた自動ブラウザ分割・並列処理
  #   - データ保護・フォーム自動修復
  #   - 失敗データの詳細ログ・JSON出力
  #   - 拠点ごとにspreadsheet_id/sheet_name/element_textを個別指定可能
  intent: "カイポケ実績・看護記録データの自動入力・検証・エラー収集（リファクタ・データ保護・多ブラウザ対応）"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    corporation_id_env: KAIPOKE_CORPORATION_ID
    login_id_env: KAIPOKE_MEMBER_LOGIN_ID
    password_env: KAIPOKE_PASSWORD
    headless: false  # テスト時はfalse、本番はtrue推奨
    facilities:
      - name: "荒田"
        element_text: "訪問看護/4660190861"
        spreadsheet_id: "1AwLxoHHt8YfM2n7hZ2mtgKlVP3sSUxfRvsFLaF_1eXQ"
        sheet_name: "看護記録"

    # 追加設定例（必要に応じて拡張可）
    # max_browser_count: 4
    # data_volume_threshold:
    #   small: 50
    #   medium: 150
    #   large: 300
kaipoke_form_download:
  intent: "カイポケにログインし、定義されたタスクリストに基づき、様々な様式の月次レポートをダウンロードする"
  config:
    login_url: "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
    corporation_id_env: "KAIPOKE_CORPORATION_ID"
    member_login_id_env: "KAIPOKE_MEMBER_LOGIN_ID"
    password_env: "KAIPOKE_PASSWORD"
    download_path: "/tmp/kaipoke_form_downloads"
    gdrive_folder_id: "1tOnAiIDrNDafJmZrLSr2EL95Oq0v-CA0"
    headless: false # For debugging, set to true in production
  tasks:
    # 4.1 グループ: あおぞら介護ステーション (鹿儿岛)
    - task_id: "kagoshima_01"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "訪問介護/4670105586"
      output_filename: "訪問介護鹿児島.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "kagoshima_02"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "訪問介護/4670105586"
      output_filename: "訪問介護鹿児島2-3.pdf"
      flow_type: "general"
      click_selector_2: "div:nth-of-type(5) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "kagoshima_03"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "障害者総合支援(訪問系)/4610101257"
      output_filename: "訪問介護鹿児島障害.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(3) tr:nth-of-type(2) .column02 :nth-child(1)"
    - task_id: "kagoshima_04"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "訪問看護/4660190861"
      output_filename: "訪問看護鹿児島 介護.pdf"
      flow_type: "general"
      click_selector_2: ".box-refine :nth-child(1)"
    - task_id: "kagoshima_05"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "訪問看護/4660190861"
      output_filename: "訪問看護鹿児島 国保・後期.pdf"
      flow_type: "special_1"
      click_selector_2: "table:nth-of-type(2) .btn"
    - task_id: "kagoshima_07"
      facility_name: "あおぞら介護ステーション（鹿児島）"
      element_text: "訪問看護/4660190861"
      output_filename: "訪問看護鹿児島 社保.pdf"
      flow_type: "special_1"
      click_selector_2: ".table:nth-child(6) .btn"

    # 4.2 グループ: あおぞら介護ステーション福岡 (福冈)
    - task_id: "fukuoka_08"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問介護/4071404182"
      output_filename: "訪問介護福岡.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "fukuoka_09"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問介護/4071404182"
      output_filename: "訪問介護福岡2-3.pdf"
      flow_type: "general"
      click_selector_2: "div:nth-of-type(5) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "fukuoka_14"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "障害者総合支援(訪問系)/4011000983"
      output_filename: "障害福岡.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(3) tr:nth-of-type(2) .column02 :nth-child(1)"
    - task_id: "fukuoka_10"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問看護/4060391200"
      output_filename: "訪問看護福岡 介護.pdf"
      flow_type: "special_2_general"
      click_selector_2: ".box-refine :nth-child(1)"
    - task_id: "fukuoka_11"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問看護/4060391200"
      output_filename: "訪問看護福岡 国保・後期.pdf"
      flow_type: "special_2_special_1"
      click_selector_2: "table:nth-of-type(2) .btn"
    - task_id: "fukuoka_13"
      facility_name: "あおぞら介護ステーション福岡"
      element_text: "訪問看護/4060391200"
      output_filename: "訪問看護福岡 社保.pdf"
      flow_type: "special_2_special_1"
      click_selector_2: ".table:nth-child(7) .btn"

    # 4.3 グループ: 訪問看護ステーションあおぞら姶良 (姶良)
    - task_id: "aira_26"
      facility_name: "訪問看護ステーションあおぞら姶良"
      element_text: "訪問看護/4664590280"
      output_filename: "訪問看護姶良 介護.pdf"
      flow_type: "special_2_general"
      click_selector_2: ".box-refine :nth-child(1)"
    - task_id: "aira_27"
      facility_name: "訪問看護ステーションあおぞら姶良"
      element_text: "訪問看護/4664590280"
      output_filename: "訪問看護姶良 国保・后期.pdf"
      flow_type: "special_1"
      click_selector_2: "table:nth-of-type(2) .btn"
    - task_id: "aira_29"
      facility_name: "訪問看護ステーションあおぞら姶良"
      element_text: "訪問看護/4664590280"
      output_filename: "訪問看護姶良 社保.pdf"
      flow_type: "special_1"
      click_selector_2: ".table:nth-child(6) .btn"

    # 4.4 グループ: 訪問看護ステーションあおぞら谷山 (谷山)
    - task_id: "taniyama_30"
      facility_name: "訪問看護ステーションあおぞら谷山"
      element_text: "訪問看護/4660191471"
      output_filename: "訪問看護谷山 介護.pdf"
      flow_type: "general"
      click_selector_2: ".box-refine :nth-child(1)"
    - task_id: "taniyama_31"
      facility_name: "訪問看護ステーションあおぞら谷山"
      element_text: "訪問看護/4660191471"
      output_filename: "訪問看護谷山 国保・后期.pdf"
      flow_type: "special_1"
      click_selector_2: "table:nth-of-type(2) .btn"
    - task_id: "taniyama_33"
      facility_name: "訪問看護ステーションあおぞら谷山"
      element_text: "訪問看護/4660191471"
      output_filename: "訪問看護谷山 社保.pdf"
      flow_type: "special_1"
      click_selector_2: ".table:nth-child(6) .btn"

    # 4.5 グループ: その他のサービス (其他服务与日间照料中心)
    - task_id: "other_15"
      facility_name: "福祉用具貸与"
      element_text: "福祉用具貸与/4670113242"
      output_filename: "福祉用具貸与.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(3) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_16"
      facility_name: "田上"
      element_text: "通所介護/4690102746"
      output_filename: "田上.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_17"
      facility_name: "田上"
      element_text: "通所介護/4690102746"
      output_filename: "田上2-3.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(5) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_18"
      facility_name: "あおぞらデイ"
      element_text: "通所介護/4670106956"
      output_filename: "あおぞらデイ.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_20"
      facility_name: "下荒田"
      element_text: "通所介護/4670112806"
      output_filename: "下荒田.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_21"
      facility_name: "下荒田"
      element_text: "通所介護/4670112806"
      output_filename: "下荒田2-3.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(5) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_22"
      facility_name: "南栄"
      element_text: "通所介護/4670113358"
      output_filename: "南栄.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_23"
      facility_name: "南栄"
      element_text: "通所介護/4670113358"
      output_filename: "南栄2-3.pdf"
      flow_type: "general"
      click_selector_2: "div:nth-of-type(5) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_24"
      facility_name: "博多"
      element_text: "通所介護/4070904703"
      output_filename: "博多.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4) tr:nth-of-type(2) a:nth-of-type(1)"
    - task_id: "other_34"
      facility_name: "東千石"
      element_text: "通所介護/4670113754"
      output_filename: "東千石.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4)" # Selector needs verification
    - task_id: "other_35"
      facility_name: "東千石"
      element_text: "通所介護/4670113754"
      output_filename: "東千石2-3.pdf"
      flow_type: "general"
      click_selector_2: "div:nth-of-type(5)" # Selector needs verification
    - task_id: "other_36"
      facility_name: "永吉"
      element_text: "通所介護/4690103066"
      output_filename: "永吉.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4)" # Selector needs verification
    - task_id: "other_37"
      facility_name: "野芥"
      element_text: "通所介護/4071405262"
      output_filename: "野芥.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4)" # Selector needs verification
    - task_id: "other_43"
      facility_name: "野芥"
      element_text: "通所介護/4071405262"
      output_filename: "野芥2-3.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(5)" # Selector needs verification
    - task_id: "other_38"
      facility_name: "七福"
      element_text: "通所介護/4690103124"
      output_filename: "七福.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(4)" # Selector needs verification
    - task_id: "other_39"
      facility_name: "七福"
      element_text: "通所介護/4690103124"
      output_filename: "七福2-3.pdf"
      flow_type: "special_2_general"
      click_selector_2: "div:nth-of-type(5)" # Selector needs verification
    - task_id: "other_40"
      facility_name: "うらら"
      element_text: "通所介護/4691200622"
      output_filename: "うらら.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4)" # Selector needs verification
    - task_id: "other_41"
      facility_name: "うらら"
      element_text: "通所介護/4691200622"
      output_filename: "うらら2-3.pdf"
      flow_type: "general"
      click_selector_2: "div:nth-of-type(5)" # Selector needs verification
    - task_id: "other_42"
      facility_name: "宇美"
      element_text: "通所介護/4073800759"
      output_filename: "宇美.pdf"
      flow_type: "general"
      click_selector_2: ".box-input:nth-child(4)" # Selector needs verification